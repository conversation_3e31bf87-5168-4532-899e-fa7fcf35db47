# 🏗️ New Architecture Design - Brand Content Generator

## 🎯 Vision Statement

Build an application that creates on-brand image material given:
- **Brand Name**: Any brand
- **Goal**: Flexible, user-defined goal (e.g., "LinkedIn Carousel", "Instagram Story", "Product Launch Banner")
- **Article/Content**: Campaign information, knowledge base articles, or any content to visualize

## 🔄 Current Problem

The existing system has architectural issues:

1. **Tight Coupling**: Brand research is coupled with predefined goals
2. **Fixed Goals**: Limited to 6 predefined content goals
3. **Search Query Dependency**: Search queries are goal-specific, limiting brand research completeness
4. **Missing Article Support**: No support for article/content input
5. **Inflexible Output**: Cannot handle arbitrary user-defined goals

## 🏗️ New Architecture

### Phase 1: Brand Research (Goal-Agnostic)
```
Input: Brand Name
Process: Comprehensive brand profiling
Output: Complete brand JSON (like coca-cola_enhanced.json)
```

**Key Changes:**
- Remove goal dependency from brand research
- Use comprehensive search queries focused on brand identity
- Create complete brand profiles regardless of intended use
- Cache brand profiles for reuse

### Phase 2: Goal Interpretation (Dynamic)
```
Input: User Goal (any string)
Process: AI-powered goal analysis
Output: Image generation requirements
```

**Key Features:**
- Accept any goal description (not limited to predefined options)
- Use AI to interpret goal requirements:
  - Single vs. multiple images
  - Image dimensions/format
  - Content structure needs
  - Visual style requirements

### Phase 3: Content Generation (Article-Driven)
```
Input: Brand Profile + Goal Requirements + Article/Content
Process: AI-powered image generation
Output: On-brand images tailored to goal and content
```

**Key Features:**
- Combine brand identity with article content
- Generate images that match goal requirements
- Maintain brand consistency across all outputs
- Support various image formats and quantities

## 🔧 Implementation Plan

### 1. Decouple Brand Research
- Modify `main_agent.py` to be goal-agnostic
- Update search queries to focus on comprehensive brand data
- Remove goal-specific search logic

### 2. Create Goal Processor
- New module: `app/goal_processor.py`
- AI-powered goal interpretation
- Dynamic requirement generation

### 3. Create Article Processor
- New module: `app/article_processor.py`
- Content analysis and extraction
- Integration with brand profile

### 4. Create Image Generator
- New module: `app/image_generator.py`
- Brand + Goal + Article → Images
- Support for multiple image generation

### 5. Update Main Interface
- New unified entry point
- Support for article input
- Flexible goal definitions

## 📁 New File Structure

```
app/
├── brand_research/          # Goal-agnostic brand profiling
│   ├── __init__.py
│   ├── researcher.py        # Main brand research logic
│   └── search_strategies.py # Comprehensive search queries
├── goal_processing/         # Dynamic goal interpretation
│   ├── __init__.py
│   ├── goal_processor.py    # AI-powered goal analysis
│   └── requirements_generator.py
├── content_generation/      # Article + Brand → Images
│   ├── __init__.py
│   ├── article_processor.py # Content analysis
│   ├── image_generator.py   # Image creation
│   └── prompt_builder.py    # Dynamic prompt generation
└── core/                    # Shared utilities
    ├── __init__.py
    ├── openai_client.py     # Enhanced OpenAI integration
    └── schemas.py           # Core data structures
```

## 🎯 New User Experience

### Command Line Interface
```bash
# Brand research only (if needed)
python brand_generator.py research "Coca-Cola"

# Generate content with article
python brand_generator.py generate "Coca-Cola" \
  --goal "LinkedIn Carousel about sustainability" \
  --article "path/to/article.txt"

# Generate content with inline content
python brand_generator.py generate "Nike" \
  --goal "Instagram Story for new product launch" \
  --content "Introducing our new eco-friendly running shoes..."
```

### API Interface
```python
# Research brand (if not cached)
brand_profile = research_brand("Coca-Cola")

# Generate content
images = generate_content(
    brand_profile=brand_profile,
    goal="LinkedIn Carousel about sustainability",
    article_content="Our commitment to sustainable packaging..."
)
```

## 🔄 Migration Strategy

1. **Preserve Existing Functionality**: Keep current system working
2. **Gradual Migration**: Implement new modules alongside existing ones
3. **Backward Compatibility**: Support existing goal definitions during transition
4. **Testing**: Comprehensive testing of new architecture
5. **Documentation**: Update guides and examples

## 🎯 Success Metrics

- ✅ Brand research is goal-agnostic and comprehensive
- ✅ Any user-defined goal can be processed
- ✅ Article content drives image generation
- ✅ Multiple image formats supported
- ✅ Brand consistency maintained across all outputs
- ✅ Flexible and extensible architecture
