"""
Flexible goal processing system that can interpret any user-defined goal
and translate it to appropriate image generation requirements.
"""

import json
from typing import Dict, List, Optional, Union
from dataclasses import dataclass

from .openai_client import OpenAIClient


@dataclass
class ImageRequirement:
    """Represents requirements for a single image."""
    purpose: str  # e.g., "title slide", "content slide 1", "hero image"
    content_focus: str  # What the image should focus on
    design_notes: str  # Additional design considerations


@dataclass
class GoalRequirements:
    """Represents the complete requirements for a user goal."""
    goal_description: str
    content_type: str  # e.g., "social_media", "presentation", "marketing"
    image_count: int
    images: List[ImageRequirement]
    overall_style: str
    brand_elements_needed: List[str]
    additional_notes: str


class GoalProcessor:
    """
    Processes flexible user-defined goals and translates them to image generation requirements.
    """
    
    def __init__(self):
        self.openai_client = OpenAIClient()
    
    def process_goal(self, goal_description: str) -> GoalRequirements:
        """
        Process a user-defined goal and return structured requirements.
        
        Args:
            goal_description: Natural language description of the goal
            
        Returns:
            GoalRequirements object with structured image generation requirements
        """
        print(f"Processing goal: {goal_description}")
        
        # Use AI to interpret the goal and generate requirements
        requirements_json = self._analyze_goal_with_ai(goal_description)
        
        if not requirements_json:
            # Fallback to basic single image if AI analysis fails
            return self._create_fallback_requirements(goal_description)
        
        # Convert JSON to GoalRequirements object
        return self._json_to_requirements(requirements_json, goal_description)
    
    def _analyze_goal_with_ai(self, goal_description: str) -> Optional[Dict]:
        """Use AI to analyze the goal and generate structured requirements."""
        
        prompt = f"""Analyze the following content creation goal and provide structured requirements for image generation.

Goal: "{goal_description}"

Based on this goal, determine:
1. What type of content this is (social media, presentation, marketing material, etc.)
2. How many images are needed
3. What each image should contain and its purpose
4. Appropriate dimensions and formats
5. Overall style and brand elements needed

Provide your analysis in the following JSON format:

{{
    "content_type": "string (e.g., social_media, presentation, marketing, editorial)",
    "image_count": "integer (1-10)",
    "overall_style": "string describing the overall visual style needed",
    "brand_elements_needed": ["list", "of", "brand", "elements", "to", "include"],
    "images": [
        {{
            "purpose": "string describing what this image is for",
            "content_focus": "string describing what the image should focus on",
            "design_notes": "string with additional design considerations"
        }}
    ],
    "additional_notes": "string with any additional considerations"
}}

Examples of goals and their interpretations:

Goal: "LinkedIn Carousel about sustainability"
→ content_type: "social_media", image_count: 5-8, images for title slide + content slides + CTA

Goal: "Instagram Story for product launch"
→ content_type: "social_media", image_count: 3-5, vertical format, story-style layout

Goal: "Hero banner for website homepage"
→ content_type: "web", image_count: 1, landscape format, hero-style composition

Goal: "Email newsletter header"
→ content_type: "email_marketing", image_count: 1, wide landscape format

Goal: "Product showcase for e-commerce"
→ content_type: "e-commerce", image_count: 3-6, product-focused images

Analyze the goal and provide the JSON structure:"""

        try:
            # Use the OpenAI client to analyze the goal
            response = self.openai_client.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "You are an expert in content creation and visual design. Analyze user goals and provide structured requirements for image generation. Always respond with valid JSON only."},
                    {"role": "user", "content": prompt}
                ],
                response_format={"type": "json_object"}
            )
            
            response_text = response.choices[0].message.content.strip()
            
            # Try to extract JSON from the response
            if response_text.startswith('```json'):
                response_text = response_text[7:-3].strip()
            elif response_text.startswith('```'):
                response_text = response_text[3:-3].strip()

            print(f"[DEBUG] AI response Goal Processor: {json.loads(response_text)}")
            
            return json.loads(response_text)
            
        except Exception as e:
            print(f"Error analyzing goal with AI: {e}")
            return None
    
    def _create_fallback_requirements(self, goal_description: str) -> GoalRequirements:
        """Create basic fallback requirements if AI analysis fails."""
        print("Using fallback requirements for goal processing")
        
        return GoalRequirements(
            goal_description=goal_description,
            content_type="general",
            image_count=1,
            images=[
                ImageRequirement(
                    purpose="Main image",
                    content_focus=goal_description,
                    design_notes="Create an on-brand image that addresses the goal"
                )
            ],
            overall_style="Clean and professional, following brand guidelines",
            brand_elements_needed=["logo", "brand_colors", "typography"],
            additional_notes="Fallback requirements - consider refining the goal description"
        )
    
    def _json_to_requirements(self, requirements_json: Dict, goal_description: str) -> GoalRequirements:
        """Convert JSON requirements to GoalRequirements object."""
        try:
            images = []
            for img_data in requirements_json.get("images", []):
                images.append(ImageRequirement(
                    purpose=img_data.get("purpose", "Image"),
                    content_focus=img_data.get("content_focus", "Brand content"),
                    design_notes=img_data.get("design_notes", "")
                ))
            
            return GoalRequirements(
                goal_description=goal_description,
                content_type=requirements_json.get("content_type", "general"),
                image_count=requirements_json.get("image_count", len(images)),
                images=images,
                overall_style=requirements_json.get("overall_style", "Professional and on-brand"),
                brand_elements_needed=requirements_json.get("brand_elements_needed", ["logo", "brand_colors"]),
                additional_notes=requirements_json.get("additional_notes", "")
            )
            
        except Exception as e:
            print(f"Error converting JSON to requirements: {e}")
            return self._create_fallback_requirements(goal_description)


def process_goal(goal_description: str) -> GoalRequirements:
    """
    Convenience function for goal processing.
    
    Args:
        goal_description: Natural language description of the goal
        
    Returns:
        GoalRequirements object with structured image generation requirements
    """
    processor = GoalProcessor()
    return processor.process_goal(goal_description)
