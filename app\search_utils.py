"""
Search utilities for finding brand-related URLs using Google search.
"""
import re
import time
from typing import List, Set
from urllib.parse import urlparse, urljoin
import requests
from bs4 import BeautifulSoup
from googlesearch import search


def get_comprehensive_brand_queries(brand_name: str) -> List[str]:
    """
    Get comprehensive, goal-agnostic search queries for brand research.
    These queries focus on gathering complete brand information regardless of intended use.

    Args:
        brand_name: Name of the brand to search for

    Returns:
        List of search queries for comprehensive brand research
    """
    return [
        # Core brand identity
        f'"{brand_name}" brand guidelines',
        f'"{brand_name}" style guide',
        f'"{brand_name}" brand identity',
        f'"{brand_name}" brand standards',
        f'"{brand_name}" visual identity',

        # Official sources
        f'"{brand_name}" official website about',
        f'"{brand_name}" company information',
        f'"{brand_name}" press kit',
        f'"{brand_name}" media resources',

        # Brand assets and design
        f'"{brand_name}" logo usage',
        f'"{brand_name}" color palette',
        f'"{brand_name}" typography',
        f'"{brand_name}" brand assets',

        # Brand strategy and messaging
        f'"{brand_name}" mission vision values',
        f'"{brand_name}" brand story',
        f'"{brand_name}" brand positioning',
        f'"{brand_name}" tone of voice',

        # Marketing and communications
        f'"{brand_name}" marketing guidelines',
        f'"{brand_name}" communication standards',
        f'"{brand_name}" brand voice',
        f'"{brand_name}" messaging framework'
    ]


def clean_url(url: str) -> str:
    """Clean and normalize URL."""
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url
    return url.strip()


def is_relevant_brand_url(url: str, brand_name: str) -> bool:
    """
    Check if a URL is likely to contain relevant brand information.
    
    Args:
        url: The URL to check
        brand_name: The brand name to match against
        
    Returns:
        bool: True if URL seems relevant for brand information
    """
    url_lower = url.lower()
    brand_lower = brand_name.lower().replace(' ', '').replace('-', '')
    
    # Check if brand name is in domain
    domain = urlparse(url_lower).netloc
    if brand_lower in domain.replace('.', '').replace('-', ''):
        return True
    
    # Check for brand-related keywords in URL path
    brand_keywords = [
        'brand', 'guidelines', 'identity', 'style', 'guide',
        'about', 'company', 'press', 'media', 'assets',
        'logo', 'design', 'marketing', 'resources'
    ]
    
    for keyword in brand_keywords:
        if keyword in url_lower:
            return True
    
    return False


def filter_urls_by_relevance(urls: List[str], brand_name: str) -> List[str]:
    """
    Filter URLs by relevance to brand information.
    
    Args:
        urls: List of URLs to filter
        brand_name: Brand name to match against
        
    Returns:
        List of filtered URLs, prioritized by relevance
    """
    relevant_urls = []
    
    # Priority keywords for sorting
    priority_keywords = ['brand', 'guidelines', 'identity', 'style-guide']
    secondary_keywords = ['about', 'company', 'press', 'media']
    
    for url in urls:
        if is_relevant_brand_url(url, brand_name):
            relevant_urls.append(url)
    
    # Sort by priority
    def url_priority(url):
        url_lower = url.lower()
        if any(keyword in url_lower for keyword in priority_keywords):
            return 0  # Highest priority
        elif any(keyword in url_lower for keyword in secondary_keywords):
            return 1  # Medium priority
        else:
            return 2  # Lower priority
    
    relevant_urls.sort(key=url_priority)
    return relevant_urls


def search_brand_urls(brand_name: str, max_results: int = 10, custom_queries: List[str] = None) -> List[str]:
    """
    Search for brand-related URLs using Google search.

    Args:
        brand_name: Name of the brand to search for
        max_results: Maximum number of search results to process
        custom_queries: Optional list of custom search queries (will be formatted with brand_name)

    Returns:
        List of relevant URLs for the brand
    """
    if custom_queries:
        # Use custom queries, format them with brand_name
        search_queries = [query.format(brand_name=brand_name) for query in custom_queries]
    else:
        # Use comprehensive brand research queries (goal-agnostic)
        search_queries = get_comprehensive_brand_queries(brand_name)
    
    all_urls = set()
    
    for query in search_queries:
        try:
            print(f"Searching: {query}")
            # Use googlesearch-python library
            search_results = search(query, num_results=max_results//len(search_queries) + 2, sleep_interval=1)
            
            for url in search_results:
                cleaned_url = clean_url(url)
                all_urls.add(cleaned_url)
                
            # Add small delay between searches to be respectful
            time.sleep(2)
            
        except Exception as e:
            print(f"Error searching for '{query}': {e}")
            continue
    
    # Convert to list and filter by relevance
    url_list = list(all_urls)
    relevant_urls = filter_urls_by_relevance(url_list, brand_name)
    
    # Limit to reasonable number of URLs to process
    return relevant_urls[:8]


def get_page_title(url: str) -> str:
    """
    Get the title of a webpage.
    
    Args:
        url: URL to get title from
        
    Returns:
        Page title or empty string if unable to fetch
    """
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        title_tag = soup.find('title')
        
        if title_tag:
            return title_tag.get_text().strip()
        
    except Exception as e:
        print(f"Could not get title for {url}: {e}")
    
    return ""


def extract_text_content(url: str, max_length: int = 5000) -> str:
    """
    Extract text content from a webpage.
    
    Args:
        url: URL to extract content from
        max_length: Maximum length of text to extract
        
    Returns:
        Extracted text content
    """
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Remove script and style elements
        for script in soup(["script", "style", "nav", "footer", "header"]):
            script.decompose()
        
        # Get text content
        text = soup.get_text()
        
        # Clean up text
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)
        
        # Limit length
        if len(text) > max_length:
            text = text[:max_length] + "..."
        
        return text
        
    except Exception as e:
        print(f"Could not extract text from {url}: {e}")
        return f"[Could not extract text content from {url}]"


def search_brand_info(brand_name: str, custom_queries: List[str] = None, max_results: int = 10) -> List[tuple]:
    """
    Search for brand information and return URLs with extracted content.

    Args:
        brand_name: Name of the brand to search for
        custom_queries: Optional list of custom search queries
        max_results: Maximum number of search results to process

    Returns:
        List of tuples (url, content) where content is extracted text
    """
    # Get URLs using existing search function
    urls = search_brand_urls(brand_name, max_results, custom_queries)

    # Extract content for each URL
    urls_and_content = []
    for url in urls:
        try:
            title = get_page_title(url)
            content = extract_text_content(url)

            # Combine title and content
            full_content = f"{title}\n{content}" if title else content
            urls_and_content.append((url, full_content))

        except Exception as e:
            print(f"Error processing {url}: {e}")
            # Still include the URL but with empty content
            urls_and_content.append((url, ""))

    return urls_and_content
